import cv2
import numpy as np
import matplotlib.pyplot as plt
from itertools import combinations
import math

class EnhancedSquareDetector:
    def __init__(self):
        # 针对复杂多边形优化的参数
        self.corner_quality = 0.001     # 更低的质量阈值
        self.corner_min_distance = 5    # 更小的最小距离
        self.angle_tolerance = 25       # 更大的角度容差
        self.side_length_tolerance = 0.4 # 更大的边长容差
        self.min_square_size = 10       # 更小的最小尺寸
        self.max_square_size = 1000     # 更大的最大尺寸
        
    def preprocess_image(self, img_path):
        """增强的图像预处理"""
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")
        
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 多种二值化方法
        # 方法1: 全局阈值
        _, binary1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 方法2: 自适应阈值
        binary2 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY, 11, 2)
        
        # 组合两种方法
        binary = cv2.bitwise_or(binary1, binary2)
        
        # 形态学操作
        kernel = np.ones((3,3), np.uint8)
        binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        binary = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel)
        
        # 多尺度边缘检测
        edges1 = cv2.Canny(binary, 30, 100)
        edges2 = cv2.Canny(gray, 50, 150)
        edges = cv2.bitwise_or(edges1, edges2)
        
        # 膨胀操作连接断开的边缘
        kernel = np.ones((2,2), np.uint8)
        edges = cv2.dilate(edges, kernel, iterations=1)
        
        return img, gray, binary, edges
    
    def detect_corners_multi_method(self, gray, edges):
        """多方法角点检测"""
        corners_list = []
        
        # 方法1: goodFeaturesToTrack
        corners1 = cv2.goodFeaturesToTrack(
            gray, maxCorners=500, qualityLevel=self.corner_quality,
            minDistance=self.corner_min_distance, useHarrisDetector=True
        )
        if corners1 is not None:
            corners_list.extend(corners1.reshape(-1, 2))
        
        # 方法2: 在边缘图上检测角点
        corners2 = cv2.goodFeaturesToTrack(
            edges, maxCorners=300, qualityLevel=0.01,
            minDistance=self.corner_min_distance
        )
        if corners2 is not None:
            corners_list.extend(corners2.reshape(-1, 2))
        
        # 方法3: 轮廓角点
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            if cv2.contourArea(contour) > 50:
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                for point in approx:
                    corners_list.append(point[0])
        
        if not corners_list:
            return np.array([])
        
        # 去重和过滤
        corners = np.array(corners_list)
        corners = self.remove_duplicate_corners(corners)
        
        return corners
    
    def remove_duplicate_corners(self, corners):
        """去除重复的角点"""
        if len(corners) == 0:
            return corners
        
        unique_corners = []
        for corner in corners:
            is_duplicate = False
            for existing in unique_corners:
                if np.linalg.norm(corner - existing) < self.corner_min_distance:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_corners.append(corner)
        
        return np.array(unique_corners)
    
    def detect_lines_advanced(self, edges):
        """高级直线检测"""
        # 标准霍夫变换
        lines1 = cv2.HoughLines(edges, 1, np.pi/180, threshold=80)
        
        # 概率霍夫变换
        lines2 = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                minLineLength=20, maxLineGap=15)
        
        all_lines = []
        
        # 处理标准霍夫变换结果
        if lines1 is not None:
            for line in lines1:
                rho, theta = line[0]
                a = np.cos(theta)
                b = np.sin(theta)
                x0 = a * rho
                y0 = b * rho
                x1 = int(x0 + 1000 * (-b))
                y1 = int(y0 + 1000 * (a))
                x2 = int(x0 - 1000 * (-b))
                y2 = int(y0 - 1000 * (a))
                all_lines.append([x1, y1, x2, y2])
        
        # 处理概率霍夫变换结果
        if lines2 is not None:
            for line in lines2:
                all_lines.append(line[0])
        
        return all_lines
    
    def find_line_intersections(self, lines):
        """找到直线交点"""
        intersections = []
        
        for i, line1 in enumerate(lines):
            for j, line2 in enumerate(lines[i+1:], i+1):
                intersection = self.line_intersection(line1, line2)
                if intersection is not None:
                    intersections.append(intersection)
        
        return np.array(intersections)
    
    def line_intersection(self, line1, line2):
        """计算两条直线的交点"""
        x1, y1, x2, y2 = line1
        x3, y3, x4, y4 = line2
        
        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            return None  # 平行线
        
        t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        
        x = x1 + t*(x2-x1)
        y = y1 + t*(y2-y1)
        
        # 检查交点是否在合理范围内
        if 0 <= x <= 2000 and 0 <= y <= 2000:  # 假设图像不会超过2000x2000
            return np.array([x, y])
        
        return None
    
    def is_valid_square_relaxed(self, points):
        """放宽条件的正方形验证"""
        if len(points) != 4:
            return False
        
        # 计算所有边长
        sides = []
        for i in range(4):
            p1 = points[i]
            p2 = points[(i + 1) % 4]
            sides.append(np.linalg.norm(p1 - p2))
        
        # 检查边长
        avg_side = np.mean(sides)
        if avg_side < self.min_square_size or avg_side > self.max_square_size:
            return False
        
        # 放宽边长相等性检查
        for side in sides:
            if abs(side - avg_side) / avg_side > self.side_length_tolerance:
                return False
        
        # 放宽角度检查
        angles = []
        for i in range(4):
            p1 = points[(i - 1) % 4]
            p2 = points[i]
            p3 = points[(i + 1) % 4]
            
            v1 = p1 - p2
            v2 = p3 - p2
            
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1, 1)
            angle = np.arccos(cos_angle) * 180 / np.pi
            angles.append(angle)
        
        # 检查是否接近90度
        for angle in angles:
            if abs(angle - 90) > self.angle_tolerance:
                return False
        
        return True
    
    def detect_squares(self, img_path):
        """主检测函数"""
        print(f"开始处理: {img_path}")
        
        # 预处理
        img, gray, binary, edges = self.preprocess_image(img_path)
        
        # 多方法角点检测
        corners = self.detect_corners_multi_method(gray, edges)
        print(f"检测到 {len(corners)} 个角点")
        
        # 直线检测和交点计算
        lines = self.detect_lines_advanced(edges)
        print(f"检测到 {len(lines)} 条直线")
        
        if lines:
            line_intersections = self.find_line_intersections(lines)
            print(f"计算出 {len(line_intersections)} 个直线交点")
            
            # 合并角点和交点
            all_points = np.vstack([corners, line_intersections]) if len(corners) > 0 else line_intersections
            all_points = self.remove_duplicate_corners(all_points)
        else:
            all_points = corners
        
        print(f"总共 {len(all_points)} 个候选点")
        
        # 寻找正方形
        squares = []
        if len(all_points) >= 4:
            # 限制组合数量以避免计算爆炸
            max_points = min(len(all_points), 50)
            selected_points = all_points[:max_points]
            
            print(f"从 {len(selected_points)} 个点中寻找正方形...")
            
            count = 0
            for combo in combinations(range(len(selected_points)), 4):
                count += 1
                if count % 10000 == 0:
                    print(f"已检查 {count} 个组合...")
                
                four_points = selected_points[list(combo)]
                
                if self.is_valid_square_relaxed(four_points):
                    # 按顺序排列点
                    ordered_points = self.order_points(four_points)
                    squares.append(ordered_points)
                    print(f"找到正方形 #{len(squares)}")
                
                # 限制最大检查数量
                if count > 50000:
                    print("达到最大检查数量限制")
                    break
        
        # 去重
        unique_squares = self.remove_duplicate_squares(squares)
        
        # 可视化
        result_img = self.visualize_results(img, all_points, unique_squares)
        
        return result_img, all_points, unique_squares
    
    def order_points(self, points):
        """按顺时针顺序排列点"""
        center = np.mean(points, axis=0)
        angles = []
        for point in points:
            angle = math.atan2(point[1] - center[1], point[0] - center[0])
            angles.append(angle)
        
        sorted_indices = np.argsort(angles)
        return points[sorted_indices]
    
    def remove_duplicate_squares(self, squares):
        """去除重复的正方形"""
        if len(squares) <= 1:
            return squares
        
        unique_squares = []
        for square in squares:
            is_duplicate = False
            center = np.mean(square, axis=0)
            
            for existing in unique_squares:
                existing_center = np.mean(existing, axis=0)
                if np.linalg.norm(center - existing_center) < 30:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_squares.append(square)
        
        return unique_squares
    
    def visualize_results(self, img, points, squares):
        """可视化结果"""
        result_img = img.copy()
        
        # 绘制所有检测点
        for point in points:
            cv2.circle(result_img, tuple(point.astype(int)), 2, (0, 255, 0), -1)
        
        # 绘制正方形
        colors = [(255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), 
                 (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 0)]
        
        for i, square in enumerate(squares):
            color = colors[i % len(colors)]
            
            # 绘制正方形
            pts = square.astype(np.int32)
            cv2.polylines(result_img, [pts], True, color, 4)
            
            # 绘制角点
            for point in square:
                cv2.circle(result_img, tuple(point.astype(int)), 6, color, -1)
            
            # 标注
            center = np.mean(square, axis=0).astype(int)
            cv2.putText(result_img, f'S{i+1}', tuple(center), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)
        
        return result_img

def main():
    """测试函数"""
    detector = EnhancedSquareDetector()
    
    # 查找图像文件
    import os
    image_files = [f for f in os.listdir('.') if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    
    if not image_files:
        print("没有找到图像文件")
        return
    
    for img_file in image_files:
        try:
            result_img, points, squares = detector.detect_squares(img_file)
            
            # 显示结果
            plt.figure(figsize=(15, 5))
            
            plt.subplot(1, 3, 1)
            original = cv2.imread(img_file)
            plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
            plt.title('原图')
            plt.axis('off')
            
            plt.subplot(1, 3, 2)
            _, _, _, edges = detector.preprocess_image(img_file)
            plt.imshow(edges, cmap='gray')
            plt.title(f'边缘检测\n{len(points)} 个候选点')
            plt.axis('off')
            
            plt.subplot(1, 3, 3)
            plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
            plt.title(f'检测结果\n{len(squares)} 个正方形')
            plt.axis('off')
            
            plt.tight_layout()
            plt.show()
            
            # 保存结果
            cv2.imwrite(f'enhanced_result_{img_file}', result_img)
            print(f"结果保存为: enhanced_result_{img_file}")
            
        except Exception as e:
            print(f"处理 {img_file} 时出错: {e}")

if __name__ == "__main__":
    main()
