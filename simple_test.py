import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
from square_decomposition import SquareDecomposer

def find_image_files():
    """查找当前目录下的图像文件"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    image_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(file)
    
    return image_files

def process_single_image(img_path):
    """处理单个图像"""
    print(f"\n处理图像: {img_path}")
    print("-" * 40)
    
    # 创建分解器，调整参数以适应复杂图像
    decomposer = SquareDecomposer()
    
    # 针对复杂多边形调整参数
    decomposer.corner_quality = 0.005      # 降低质量阈值，检测更多角点
    decomposer.corner_min_distance = 8     # 减小最小距离
    decomposer.angle_tolerance = 20        # 增加角度容差
    decomposer.side_length_tolerance = 0.3 # 增加边长容差
    decomposer.min_square_size = 15        # 减小最小正方形尺寸
    
    try:
        # 处理图像
        result_img, corners, squares = decomposer.decompose(img_path)
        
        # 创建对比图
        original = cv2.imread(img_path)
        
        # 显示结果
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 原图
        axes[0].imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        axes[0].set_title(f'原始图像\n{img_path}')
        axes[0].axis('off')
        
        # 边缘检测结果
        _, _, _, edges = decomposer.preprocess_image(img_path)
        axes[1].imshow(edges, cmap='gray')
        axes[1].set_title(f'边缘检测\n检测到 {len(corners)} 个角点')
        axes[1].axis('off')
        
        # 分解结果
        axes[2].imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        axes[2].set_title(f'正方形分解结果\n找到 {len(squares)} 个正方形')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.show()
        
        # 保存结果
        result_filename = f"result_{os.path.splitext(img_path)[0]}.jpg"
        cv2.imwrite(result_filename, result_img)
        print(f"✓ 结果已保存: {result_filename}")
        
        # 详细信息
        print(f"✓ 检测到 {len(corners)} 个角点")
        print(f"✓ 找到 {len(squares)} 个正方形")
        
        if squares:
            print("\n正方形详情:")
            for i, square in enumerate(squares):
                center = np.mean(square, axis=0)
                # 计算平均边长
                side_lengths = []
                for j in range(4):
                    p1 = square[j]
                    p2 = square[(j+1) % 4]
                    length = np.sqrt((p1[0]-p2[0])**2 + (p1[1]-p2[1])**2)
                    side_lengths.append(length)
                avg_side = np.mean(side_lengths)
                
                print(f"  正方形 {i+1}:")
                print(f"    中心: ({center[0]:.1f}, {center[1]:.1f})")
                print(f"    平均边长: {avg_side:.1f} 像素")
                print(f"    角点: {square.astype(int).tolist()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("正方形分解算法测试")
    print("=" * 60)
    
    # 查找图像文件
    image_files = find_image_files()
    
    if not image_files:
        print("❌ 当前目录下没有找到图像文件")
        print("\n请将你的图像文件放在当前目录下，支持的格式:")
        print("  .jpg, .jpeg, .png, .bmp, .tiff, .tif")
        return
    
    print(f"📁 找到 {len(image_files)} 个图像文件:")
    for i, img_file in enumerate(image_files, 1):
        print(f"  {i}. {img_file}")
    
    # 处理所有图像
    success_count = 0
    for img_file in image_files:
        if process_single_image(img_file):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 处理完成: {success_count}/{len(image_files)} 个图像成功")
    
    if success_count == 0:
        print("\n💡 建议:")
        print("1. 确保图像包含清晰的正方形边缘")
        print("2. 图像对比度要足够高")
        print("3. 正方形边缘要相对完整")
        print("4. 可以尝试调整算法参数")
    else:
        print(f"\n✅ 成功处理了 {success_count} 个图像")
        print("📁 结果图像已保存在当前目录下")

if __name__ == "__main__":
    main()
