import cv2
import numpy as np
import matplotlib.pyplot as plt

def process_image(image_path, output_dir="output"):
    """
    图像处理流程：二值化 -> Canny边缘检测 -> 角点检测
    
    Args:
        image_path: 输入图像路径
        output_dir: 输出目录
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return
    
    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # 创建输出目录
    import os
    os.makedirs(output_dir, exist_ok=True)
    
    # 步骤1: 二值化处理
    print("步骤1: 二值化处理...")
    
    # 方法1: 简单阈值二值化
    _, binary_simple = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
    
    # 方法2: 自适应阈值二值化（推荐）
    binary_adaptive = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
    )
    
    # 方法3: Otsu自动阈值
    _, binary_otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # 保存二值化结果
    cv2.imwrite(f"{output_dir}/binary_simple.jpg", binary_simple)
    cv2.imwrite(f"{output_dir}/binary_adaptive.jpg", binary_adaptive)
    cv2.imwrite(f"{output_dir}/binary_otsu.jpg", binary_otsu)
    
    # 步骤2: Canny边缘检测
    print("步骤2: Canny边缘检测...")
    
    # 对不同的二值化结果应用Canny算子
    canny_simple = cv2.Canny(binary_simple, 50, 150)
    canny_adaptive = cv2.Canny(binary_adaptive, 50, 150)
    canny_otsu = cv2.Canny(binary_otsu, 50, 150)
    
    # 也可以直接对灰度图应用Canny
    canny_gray = cv2.Canny(gray, 50, 150)
    
    # 保存Canny边缘检测结果
    cv2.imwrite(f"{output_dir}/canny_simple.jpg", canny_simple)
    cv2.imwrite(f"{output_dir}/canny_adaptive.jpg", canny_adaptive)
    cv2.imwrite(f"{output_dir}/canny_otsu.jpg", canny_otsu)
    cv2.imwrite(f"{output_dir}/canny_gray.jpg", canny_gray)
    
    # 步骤3: 角点检测
    print("步骤3: 角点检测...")
    
    # 方法1: Harris角点检测
    harris_corners = cv2.cornerHarris(gray, 2, 3, 0.04)
    
    # 标记Harris角点
    img_harris = img.copy()
    img_harris[harris_corners > 0.01 * harris_corners.max()] = [0, 0, 255]  # 红色标记
    
    # 方法2: Shi-Tomasi角点检测（goodFeaturesToTrack）
    corners_shi_tomasi = cv2.goodFeaturesToTrack(
        gray, 
        maxCorners=100,
        qualityLevel=0.01,
        minDistance=10,
        blockSize=3
    )
    
    # 标记Shi-Tomasi角点
    img_shi_tomasi = img.copy()
    if corners_shi_tomasi is not None:
        corners_shi_tomasi = np.int0(corners_shi_tomasi)
        for corner in corners_shi_tomasi:
            x, y = corner.ravel()
            cv2.circle(img_shi_tomasi, (x, y), 3, (0, 255, 0), -1)  # 绿色圆点
    
    # 方法3: 在Canny边缘上检测角点
    corners_canny = cv2.goodFeaturesToTrack(
        canny_gray,
        maxCorners=100,
        qualityLevel=0.01,
        minDistance=10,
        blockSize=3
    )
    
    # 标记Canny边缘角点
    img_canny_corners = img.copy()
    if corners_canny is not None:
        corners_canny = np.int0(corners_canny)
        for corner in corners_canny:
            x, y = corner.ravel()
            cv2.circle(img_canny_corners, (x, y), 3, (255, 0, 0), -1)  # 蓝色圆点
    
    # 保存角点检测结果
    cv2.imwrite(f"{output_dir}/harris_corners.jpg", img_harris)
    cv2.imwrite(f"{output_dir}/shi_tomasi_corners.jpg", img_shi_tomasi)
    cv2.imwrite(f"{output_dir}/canny_corners.jpg", img_canny_corners)
    
    # 显示结果对比
    display_results(img, gray, binary_adaptive, canny_gray, img_shi_tomasi)
    
    print(f"处理完成！结果保存在 {output_dir} 目录中")
    
    return {
        'original': img,
        'gray': gray,
        'binary': binary_adaptive,
        'canny': canny_gray,
        'corners': img_shi_tomasi
    }

def display_results(original, gray, binary, canny, corners):
    """显示处理结果对比"""
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 3, 1)
    plt.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
    plt.title('原始图像')
    plt.axis('off')
    
    plt.subplot(2, 3, 2)
    plt.imshow(gray, cmap='gray')
    plt.title('灰度图像')
    plt.axis('off')
    
    plt.subplot(2, 3, 3)
    plt.imshow(binary, cmap='gray')
    plt.title('二值化图像')
    plt.axis('off')
    
    plt.subplot(2, 3, 4)
    plt.imshow(canny, cmap='gray')
    plt.title('Canny边缘检测')
    plt.axis('off')
    
    plt.subplot(2, 3, 5)
    plt.imshow(cv2.cvtColor(corners, cv2.COLOR_BGR2RGB))
    plt.title('角点检测结果')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig('output/processing_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    # 示例用法
    image_path = "input_image.jpg"  # 请替换为你的图像路径
    
    # 检查图像是否存在
    import os
    if not os.path.exists(image_path):
        print(f"图像文件不存在: {image_path}")
        print("请将图像文件放在当前目录下，或修改 image_path 变量")
        return
    
    # 处理图像
    results = process_image(image_path)
    
    # 打印一些统计信息
    if results:
        print(f"原始图像尺寸: {results['original'].shape}")
        print(f"检测到的边缘像素数: {np.sum(results['canny'] > 0)}")

if __name__ == "__main__":
    main()
