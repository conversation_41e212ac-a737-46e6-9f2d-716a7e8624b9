import cv2
import numpy as np
import matplotlib.pyplot as plt
from itertools import combinations
import math

class SquareDecomposer:
    def __init__(self):
        # 参数设置
        self.corner_quality = 0.01      # 角点检测质量阈值
        self.corner_min_distance = 10   # 角点最小距离
        self.angle_tolerance = 15       # 角度容差（度）
        self.side_length_tolerance = 0.2  # 边长容差比例
        self.min_square_size = 20       # 最小正方形边长
        self.max_square_size = 500      # 最大正方形边长
        
    def preprocess_image(self, img_path):
        """图像预处理"""
        # 读取图像
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像: {img_path}")
            
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 二值化
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        # 边缘检测
        edges = cv2.Canny(binary, 50, 150)
        
        # 形态学操作，连接断开的边缘
        kernel = np.ones((3,3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        return img, gray, binary, edges
    
    def detect_corners(self, edges):
        """检测角点"""
        # 使用goodFeaturesToTrack检测角点
        corners = cv2.goodFeaturesToTrack(
            edges,
            maxCorners=200,
            qualityLevel=self.corner_quality,
            minDistance=self.corner_min_distance,
            useHarrisDetector=True,
            k=0.04
        )
        
        if corners is not None:
            # 转换为整数坐标
            corners = np.int32(corners).reshape(-1, 2)
            # 过滤边界附近的角点
            h, w = edges.shape
            margin = 5
            corners = corners[
                (corners[:, 0] > margin) & 
                (corners[:, 0] < w - margin) & 
                (corners[:, 1] > margin) & 
                (corners[:, 1] < h - margin)
            ]
            return corners
        return np.array([])
    
    def calculate_angle(self, p1, p2, p3):
        """计算三点形成的角度（度）"""
        v1 = np.array(p1) - np.array(p2)
        v2 = np.array(p3) - np.array(p2)
        
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1, 1)  # 防止数值误差
        angle = np.arccos(cos_angle) * 180 / np.pi
        return angle
    
    def calculate_distance(self, p1, p2):
        """计算两点间距离"""
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
    
    def is_valid_square(self, points):
        """验证四个点是否能构成正方形"""
        if len(points) != 4:
            return False
            
        # 计算所有边长
        sides = []
        for i in range(4):
            p1 = points[i]
            p2 = points[(i + 1) % 4]
            sides.append(self.calculate_distance(p1, p2))
        
        # 检查边长是否相等（在容差范围内）
        avg_side = np.mean(sides)
        if avg_side < self.min_square_size or avg_side > self.max_square_size:
            return False
            
        for side in sides:
            if abs(side - avg_side) / avg_side > self.side_length_tolerance:
                return False
        
        # 检查角度是否接近90度
        angles = []
        for i in range(4):
            p1 = points[(i - 1) % 4]
            p2 = points[i]
            p3 = points[(i + 1) % 4]
            angle = self.calculate_angle(p1, p2, p3)
            angles.append(angle)
        
        for angle in angles:
            if abs(angle - 90) > self.angle_tolerance:
                return False
        
        # 检查对角线长度是否相等
        diag1 = self.calculate_distance(points[0], points[2])
        diag2 = self.calculate_distance(points[1], points[3])
        
        if abs(diag1 - diag2) / max(diag1, diag2) > self.side_length_tolerance:
            return False
            
        return True
    
    def order_square_points(self, points):
        """按顺时针顺序排列正方形的四个点"""
        # 计算中心点
        center = np.mean(points, axis=0)
        
        # 计算每个点相对于中心的角度
        angles = []
        for point in points:
            angle = math.atan2(point[1] - center[1], point[0] - center[0])
            angles.append(angle)
        
        # 按角度排序
        sorted_indices = np.argsort(angles)
        ordered_points = points[sorted_indices]
        
        return ordered_points
    
    def find_squares(self, corners):
        """从角点中找出所有可能的正方形"""
        squares = []

        if len(corners) < 4:
            return squares

        print(f"检测到 {len(corners)} 个角点，开始寻找正方形组合...")

        # 优化：使用空间索引减少计算量
        squares = self.find_squares_optimized(corners)

        return squares

    def find_squares_optimized(self, corners):
        """优化的正方形查找算法"""
        squares = []

        # 为每个角点建立邻近点索引
        neighbor_radius = self.max_square_size * 1.5  # 搜索半径

        for i, corner in enumerate(corners):
            # 找到当前角点的邻近点
            neighbors = []
            for j, other_corner in enumerate(corners):
                if i != j:
                    dist = self.calculate_distance(corner, other_corner)
                    if self.min_square_size <= dist <= neighbor_radius:
                        neighbors.append((j, other_corner, dist))

            # 如果邻近点少于3个，跳过
            if len(neighbors) < 3:
                continue

            # 在邻近点中寻找正方形
            for combo in combinations(neighbors, 3):
                four_points = np.array([corner] + [pt[1] for pt in combo])

                # 验证是否构成正方形
                if self.is_valid_square(four_points):
                    ordered_points = self.order_square_points(four_points)
                    squares.append(ordered_points)
                    print(f"找到正方形 #{len(squares)}")

        return squares

    def detect_lines(self, edges):
        """检测直线段，用于辅助正方形检测"""
        lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                               minLineLength=30, maxLineGap=10)

        if lines is not None:
            # 分类直线：水平、垂直、斜向
            horizontal_lines = []
            vertical_lines = []
            diagonal_lines = []

            for line in lines:
                x1, y1, x2, y2 = line[0]
                angle = math.atan2(y2 - y1, x2 - x1) * 180 / np.pi

                if abs(angle) < 10 or abs(angle - 180) < 10:
                    horizontal_lines.append(line[0])
                elif abs(angle - 90) < 10 or abs(angle + 90) < 10:
                    vertical_lines.append(line[0])
                else:
                    diagonal_lines.append(line[0])

            return horizontal_lines, vertical_lines, diagonal_lines

        return [], [], []
    
    def remove_duplicate_squares(self, squares):
        """移除重复的正方形"""
        if len(squares) <= 1:
            return squares
            
        unique_squares = []
        
        for square in squares:
            is_duplicate = False
            center = np.mean(square, axis=0)
            
            for existing in unique_squares:
                existing_center = np.mean(existing, axis=0)
                # 如果中心点距离很近，认为是重复的
                if self.calculate_distance(center, existing_center) < 20:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_squares.append(square)
        
        return unique_squares
    
    def visualize_results(self, img, corners, squares):
        """可视化结果"""
        result_img = img.copy()
        
        # 绘制角点
        for corner in corners:
            cv2.circle(result_img, tuple(corner), 3, (0, 255, 0), -1)
        
        # 绘制正方形
        colors = [(255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), 
                 (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 0)]
        
        for i, square in enumerate(squares):
            color = colors[i % len(colors)]
            
            # 绘制正方形边框
            pts = np.array(square, np.int32)
            cv2.polylines(result_img, [pts], True, color, 3)
            
            # 绘制角点
            for point in square:
                cv2.circle(result_img, tuple(point.astype(int)), 5, color, -1)
            
            # 标注正方形编号
            center = np.mean(square, axis=0).astype(int)
            cv2.putText(result_img, f'S{i+1}', tuple(center), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
        
        return result_img
    
    def complete_partial_squares(self, corners, edges):
        """补线补点：从不完整的角点推断完整的正方形"""
        completed_squares = []

        # 检测直线段
        h_lines, v_lines, d_lines = self.detect_lines(edges)
        all_lines = h_lines + v_lines + d_lines

        print(f"检测到 {len(all_lines)} 条直线段")

        # 寻找可能的正方形边
        potential_sides = []
        for line in all_lines:
            x1, y1, x2, y2 = line
            length = self.calculate_distance((x1, y1), (x2, y2))
            if self.min_square_size <= length <= self.max_square_size:
                potential_sides.append(line)

        print(f"找到 {len(potential_sides)} 条可能的正方形边")

        # 尝试从边重构正方形
        for i, side1 in enumerate(potential_sides):
            for j, side2 in enumerate(potential_sides[i+1:], i+1):
                # 检查两条边是否可能属于同一个正方形
                square = self.reconstruct_square_from_sides(side1, side2)
                if square is not None:
                    completed_squares.append(square)

        return completed_squares

    def reconstruct_square_from_sides(self, side1, side2):
        """从两条边重构正方形"""
        x1, y1, x2, y2 = side1
        x3, y3, x4, y4 = side2

        # 计算边长
        len1 = self.calculate_distance((x1, y1), (x2, y2))
        len2 = self.calculate_distance((x3, y3), (x4, y4))

        # 检查边长是否相似
        if abs(len1 - len2) / max(len1, len2) > self.side_length_tolerance:
            return None

        # 检查两条边的关系（平行、垂直、相交）
        # 这里简化处理，实际应用中需要更复杂的几何分析

        return None  # 暂时返回None，需要完整的几何分析

    def decompose(self, img_path):
        """主函数：分解图像中的正方形"""
        print(f"开始处理图像: {img_path}")

        # 预处理
        img, gray, binary, edges = self.preprocess_image(img_path)

        # 检测角点
        corners = self.detect_corners(edges)
        print(f"检测到 {len(corners)} 个角点")

        if len(corners) < 4:
            print("角点数量不足，尝试补线补点...")
            # 尝试补线补点
            completed = self.complete_partial_squares(corners, edges)
            if completed:
                print(f"通过补线补点找到 {len(completed)} 个正方形")
                result_img = self.visualize_results(img, corners, completed)
                return result_img, corners, completed
            else:
                print("无法构成正方形")
                return img, [], []

        # 寻找正方形
        squares = self.find_squares(corners)
        print(f"找到 {len(squares)} 个候选正方形")

        # 尝试补线补点找到更多正方形
        completed = self.complete_partial_squares(corners, edges)
        if completed:
            squares.extend(completed)
            print(f"补线补点后总共 {len(squares)} 个正方形")

        # 移除重复
        unique_squares = self.remove_duplicate_squares(squares)
        print(f"去重后剩余 {len(unique_squares)} 个正方形")

        # 可视化结果
        result_img = self.visualize_results(img, corners, unique_squares)

        return result_img, corners, unique_squares

def main():
    # 创建分解器
    decomposer = SquareDecomposer()
    
    # 处理图像（请修改为你的图像路径）
    img_path = "C:\\Users\\<USER>\\Downloads"  # 请替换为你的图像路径
    
    try:
        result_img, corners, squares = decomposer.decompose(img_path)
        
        # 显示结果
        plt.figure(figsize=(12, 8))
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title(f'正方形分解结果 - 找到 {len(squares)} 个正方形')
        plt.axis('off')
        plt.show()
        
        # 保存结果
        cv2.imwrite('square_decomposition_result.jpg', result_img)
        print("结果已保存为 'square_decomposition_result.jpg'")
        
    except Exception as e:
        print(f"处理出错: {e}")

if __name__ == "__main__":
    main()
